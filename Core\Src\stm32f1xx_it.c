/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    stm32f1xx_it.c
  * @brief   Interrupt Service Routines.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "stm32f1xx_it.h"
/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "bsp_system.h"
#include "servo_app.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN TD */
Tripod_Struct Tripod_X, Tripod_Y;
/* USER CODE END TD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/* External variables --------------------------------------------------------*/
extern TIM_HandleTypeDef htim1;
extern TIM_HandleTypeDef htim2;
extern TIM_HandleTypeDef htim3;
extern DMA_HandleTypeDef hdma_usart1_rx;
extern DMA_HandleTypeDef hdma_usart3_rx;
extern UART_HandleTypeDef huart1;
extern UART_HandleTypeDef huart3;
/* USER CODE BEGIN EV */

/* USER CODE END EV */

/******************************************************************************/
/*           Cortex-M3 Processor Interruption and Exception Handlers          */
/******************************************************************************/
/**
  * @brief This function handles Non maskable interrupt.
  */
void NMI_Handler(void)
{
  /* USER CODE BEGIN NonMaskableInt_IRQn 0 */

  /* USER CODE END NonMaskableInt_IRQn 0 */
  /* USER CODE BEGIN NonMaskableInt_IRQn 1 */
   while (1)
  {
  }
  /* USER CODE END NonMaskableInt_IRQn 1 */
}

/**
  * @brief This function handles Hard fault interrupt.
  */
void HardFault_Handler(void)
{
  /* USER CODE BEGIN HardFault_IRQn 0 */

  /* USER CODE END HardFault_IRQn 0 */
  while (1)
  {
    /* USER CODE BEGIN W1_HardFault_IRQn 0 */
    /* USER CODE END W1_HardFault_IRQn 0 */
  }
}

/**
  * @brief This function handles Memory management fault.
  */
void MemManage_Handler(void)
{
  /* USER CODE BEGIN MemoryManagement_IRQn 0 */

  /* USER CODE END MemoryManagement_IRQn 0 */
  while (1)
  {
    /* USER CODE BEGIN W1_MemoryManagement_IRQn 0 */
    /* USER CODE END W1_MemoryManagement_IRQn 0 */
  }
}

/**
  * @brief This function handles Prefetch fault, memory access fault.
  */
void BusFault_Handler(void)
{
  /* USER CODE BEGIN BusFault_IRQn 0 */

  /* USER CODE END BusFault_IRQn 0 */
  while (1)
  {
    /* USER CODE BEGIN W1_BusFault_IRQn 0 */
    /* USER CODE END W1_BusFault_IRQn 0 */
  }
}

/**
  * @brief This function handles Undefined instruction or illegal state.
  */
void UsageFault_Handler(void)
{
  /* USER CODE BEGIN UsageFault_IRQn 0 */

  /* USER CODE END UsageFault_IRQn 0 */
  while (1)
  {
    /* USER CODE BEGIN W1_UsageFault_IRQn 0 */
    /* USER CODE END W1_UsageFault_IRQn 0 */
  }
}

/**
  * @brief This function handles System service call via SWI instruction.
  */
void SVC_Handler(void)
{
  /* USER CODE BEGIN SVCall_IRQn 0 */

  /* USER CODE END SVCall_IRQn 0 */
  /* USER CODE BEGIN SVCall_IRQn 1 */

  /* USER CODE END SVCall_IRQn 1 */
}

/**
  * @brief This function handles Debug monitor.
  */
void DebugMon_Handler(void)
{
  /* USER CODE BEGIN DebugMonitor_IRQn 0 */

  /* USER CODE END DebugMonitor_IRQn 0 */
  /* USER CODE BEGIN DebugMonitor_IRQn 1 */

  /* USER CODE END DebugMonitor_IRQn 1 */
}

/**
  * @brief This function handles Pendable request for system service.
  */
void PendSV_Handler(void)
{
  /* USER CODE BEGIN PendSV_IRQn 0 */

  /* USER CODE END PendSV_IRQn 0 */
  /* USER CODE BEGIN PendSV_IRQn 1 */

  /* USER CODE END PendSV_IRQn 1 */
}

/**
  * @brief This function handles System tick timer.
  */
void SysTick_Handler(void)
{
  /* USER CODE BEGIN SysTick_IRQn 0 */

  /* USER CODE END SysTick_IRQn 0 */
  HAL_IncTick();
  /* USER CODE BEGIN SysTick_IRQn 1 */

  /* USER CODE END SysTick_IRQn 1 */
}

/******************************************************************************/
/* STM32F1xx Peripheral Interrupt Handlers                                    */
/* Add here the Interrupt Handlers for the used peripherals.                  */
/* For the available peripheral interrupt handler names,                      */
/* please refer to the startup file (startup_stm32f1xx.s).                    */
/******************************************************************************/

/**
  * @brief This function handles DMA1 channel3 global interrupt.
  */
void DMA1_Channel3_IRQHandler(void)
{
  /* USER CODE BEGIN DMA1_Channel3_IRQn 0 */

  /* USER CODE END DMA1_Channel3_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_usart3_rx);
  /* USER CODE BEGIN DMA1_Channel3_IRQn 1 */

  /* USER CODE END DMA1_Channel3_IRQn 1 */
}

/**
  * @brief This function handles DMA1 channel5 global interrupt.
  */
void DMA1_Channel5_IRQHandler(void)
{
  /* USER CODE BEGIN DMA1_Channel5_IRQn 0 */

  /* USER CODE END DMA1_Channel5_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_usart1_rx);
  /* USER CODE BEGIN DMA1_Channel5_IRQn 1 */

  /* USER CODE END DMA1_Channel5_IRQn 1 */
}

/**
  * @brief This function handles TIM1 update interrupt.
  */
void TIM1_UP_IRQHandler(void)
{
  /* USER CODE BEGIN TIM1_UP_IRQn 0 */

  /* USER CODE END TIM1_UP_IRQn 0 */
  HAL_TIM_IRQHandler(&htim1);
  /* USER CODE BEGIN TIM1_UP_IRQn 1 */
  HAL_GPIO_TogglePin(GPIOA, GPIO_PIN_2);  //在驱动板的step引脚上输出控制脉冲
  Tripod_X.step_num--;  //脉冲个数控制

  if (Tripod_X.step_code <= 72 * 2)	//因为翻转两次为一个周期，所以*2
  {
	if ((Tripod_X.step_code - Tripod_X.step_num) < (Tripod_X.step_code / 2))   //短距离半程加速
	{	//(Tripod_X.step_code - Tripod_X.step_num)*4的最大值为72*4，所以最大分频为72分频，
		__HAL_TIM_SET_PRESCALER(&htim1, (72*5) - (Tripod_X.step_code - Tripod_X.step_num)*4);	
	}
	else	//后半程减速
	{	//(72*5) - 加速到中点时降低的分频值 + 减速时降低的分频值
		__HAL_TIM_SET_PRESCALER(&htim1, (72*5) - (Tripod_X.step_code/2)*4 + (Tripod_X.step_code - Tripod_X.step_num)*4);		
	}
  }
  else
  {
      if((Tripod_X.step_code - Tripod_X.step_num) < 72) /* 启动时加速 度过加速阶段后不变 */
      {
        __HAL_TIM_SET_PRESCALER(&htim1, (72*5) - (Tripod_X.step_code - Tripod_X.step_num)*4);
      }
      else if (Tripod_X.step_num < 72) /* 到达减速阶段 */
      {
        __HAL_TIM_SET_PRESCALER(&htim1, 72 + (72 - Tripod_X.step_num)*4);
      }
	  //两个条件都不满足时，做匀速运动，此时的预分频值为72
  }
	
  if(Tripod_X.step_num == 0) //当指定个数脉冲输出完成之后
  {
    HAL_TIM_Base_Stop_IT(&htim1);  //关闭定时器1
	Tripod_X.is_running = false;			   //X轴运动标志位清除	  
    __HAL_TIM_CLEAR_IT(&htim1, TIM_IT_UPDATE); //清除定时器1计数溢出中断标志
  }
  /* USER CODE END TIM1_UP_IRQn 1 */
}

/**
  * @brief This function handles TIM2 global interrupt.
  */
void TIM2_IRQHandler(void)
{
  /* USER CODE BEGIN TIM2_IRQn 0 */

  /* USER CODE END TIM2_IRQn 0 */
  HAL_TIM_IRQHandler(&htim2);
  /* USER CODE BEGIN TIM2_IRQn 1 */
  HAL_GPIO_TogglePin(GPIOA, GPIO_PIN_5);  //在驱动板的step引脚上输出控制脉冲
  Tripod_Y.step_num--;  //脉冲个数控制

  if (Tripod_Y.step_code <= 72 * 2)	//因为翻转两次为一个周期，所以*2
  {
	if ((Tripod_Y.step_code - Tripod_Y.step_num) < (Tripod_Y.step_code / 2))   //短距离半程加速
	{	//(Tripod_Y.step_code - Tripod_Y.step_num)*4的最大值为72*4，所以最大分频为72分频，
		__HAL_TIM_SET_PRESCALER(&htim2, (72*5) - (Tripod_Y.step_code - Tripod_Y.step_num)*4);	
	}
	else	//后半程减速
	{	//(72*5) - 加速到中点时降低的分频值 + 减速时降低的分频值
		__HAL_TIM_SET_PRESCALER(&htim2, (72*5) - (Tripod_Y.step_code/2)*4 + (Tripod_Y.step_code - Tripod_Y.step_num)*4);		
	}
  }
  else
  {
      if((Tripod_Y.step_code - Tripod_Y.step_num) < 72) /* 启动时加速 度过加速阶段后不变 */
      {
        __HAL_TIM_SET_PRESCALER(&htim2, (72*5) - (Tripod_Y.step_code - Tripod_Y.step_num)*4);
      }
      else if (Tripod_Y.step_num < 72) /* 到达减速阶段 */
      {
        __HAL_TIM_SET_PRESCALER(&htim2, 72 + (72 - Tripod_Y.step_num)*4);
      }
	  //两个条件都不满足时，做匀速运动，此时的预分频值为72
  }	
	
  if(Tripod_Y.step_num == 0) //当指定个数脉冲输出完成之后
  {
    HAL_TIM_Base_Stop_IT(&htim2);  //关闭定时器2
	Tripod_Y.is_running = false;			   //Y轴运动标志位清除  
    __HAL_TIM_CLEAR_IT(&htim2, TIM_IT_UPDATE); //清除定时器2计数溢出中断标志
  }
  /* USER CODE END TIM2_IRQn 1 */
}

/**
  * @brief This function handles TIM3 global interrupt.
  */
void TIM3_IRQHandler(void)
{
  /* USER CODE BEGIN TIM3_IRQn 0 */

  /* USER CODE END TIM3_IRQn 0 */
  HAL_TIM_IRQHandler(&htim3);
  /* USER CODE BEGIN TIM3_IRQn 1 */
	  if(interp.is_interpolating)  //如果被触发
	  {
			Interpolation_Step();
	  }
	  __HAL_TIM_CLEAR_IT(&htim3, TIM_IT_UPDATE); //清除定时器3计数溢出中断标志
  /* USER CODE END TIM3_IRQn 1 */
}

/**
  * @brief This function handles USART1 global interrupt.
  */
void USART1_IRQHandler(void)
{
  /* USER CODE BEGIN USART1_IRQn 0 */

  /* USER CODE END USART1_IRQn 0 */
  HAL_UART_IRQHandler(&huart1);
  /* USER CODE BEGIN USART1_IRQn 1 */
	HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart1_rx_dma_buf, 128);
	__HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
  /* USER CODE END USART1_IRQn 1 */
}

/**
  * @brief This function handles USART3 global interrupt.
  */
void USART3_IRQHandler(void)
{
  /* USER CODE BEGIN USART3_IRQn 0 */

  /* USER CODE END USART3_IRQn 0 */
  HAL_UART_IRQHandler(&huart3);
  /* USER CODE BEGIN USART3_IRQn 1 */
	HAL_UARTEx_ReceiveToIdle_DMA(&huart3, uart3_rx_dma_buf, 128);
	__HAL_DMA_DISABLE_IT(&hdma_usart3_rx, DMA_IT_HT);
  /* USER CODE END USART3_IRQn 1 */
}

/* USER CODE BEGIN 1 */

/* USER CODE END 1 */
