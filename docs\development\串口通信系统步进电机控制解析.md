# 串口通信系统步进电机控制解析

## 一、步进电机控制系统概述

本文档详细解析串口通信系统中的步进电机控制部分，包括电机初始化、方向控制、步进控制和绝对位置移动等核心功能。系统采用两轴步进电机控制云台，通过串口接收指令实现精确定位。

## 二、步进电机控制相关变量

### 2.1 步进电机结构体定义

```c
typedef struct Tripod_Str  //云台相关结构体
{
    volatile uint32_t step_num;  //需要运行的步数
    volatile uint32_t step_code;  //转存步数
    float angle;  //当前角度
    volatile bool is_limit;  //是否到达限位
    volatile bool is_running;  //是否正在运行
}Tripod_Struct;
```

### 2.2 关键全局变量

```c
// 步进值变量
int16_t X_Step;                // X轴步进值
int16_t Y_Step;                // Y轴步进值

// 当前位置变量
int16_t current_x, current_y;  // 当前X、Y坐标位置

// 步进电机结构体实例
Tripod_Struct Tripod_X;        // X轴步进电机控制结构体
Tripod_Struct Tripod_Y;        // Y轴步进电机控制结构体

// 步进角度常量
#define X_STEP_ANGLE    17.78f  // 云台X轴运行一度需要的步数
#define Y_STEP_ANGLE    17.78f  // 云台Y轴运行一度需要的步数
```

## 三、步进电机控制核心函数

### 3.1 电机使能控制函数

```c
// X轴电机使能
void Step_Motor_X_Cmd(void)
{
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_0, GPIO_PIN_SET);  // X轴使能引脚
}

// X轴电机禁用
void Step_Motor_X_DisCmd(void)
{
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_0, GPIO_PIN_RESET);  // X轴使能引脚
}

// Y轴电机使能
void Step_Motor_Y_Cmd(void)
{
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_3, GPIO_PIN_SET);  // Y轴使能引脚
}

// Y轴电机禁用
void Step_Motor_Y_DisCmd(void)
{
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_3, GPIO_PIN_RESET);  // Y轴使能引脚
}
```

### 3.2 电机方向控制函数

```c
// X轴方向控制: 0-左转, 1-右转
void Step_Motor_X_Dir(uint8_t x)
{
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_1, (GPIO_PinState) x);  // X轴电机转向控制
}

// Y轴方向控制: 1-上转, 0-下转
void Step_Motor_Y_Dir(uint8_t x)
{
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_4, (GPIO_PinState) x);  // Y轴电机转向控制
}
```

### 3.3 步进控制函数

```c
// X轴相对步进控制
void Tripod_X_Step(uint16_t num)
{
    if(num == 0)
        return;
    
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_2, GPIO_PIN_RESET);
    Tripod_X.step_num = num * 2;
    Tripod_X.step_code = Tripod_X.step_num;     // 转存步数
    Tripod_X.is_running = true;                 // X轴运动标志位拉高
    __HAL_TIM_CLEAR_IT(&htim1, TIM_IT_UPDATE);  // 清除溢出中断标志位
    __HAL_TIM_SET_PRESCALER(&htim1, 72 * 5);
    HAL_TIM_Base_Start_IT(&htim1);              // 以中断方式开启时钟
}

// Y轴相对步进控制
void Tripod_Y_Step(uint16_t num)
{
    if(num == 0)
        return;
    
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_5, GPIO_PIN_RESET);
    Tripod_Y.step_num = num * 2;
    Tripod_Y.step_code = Tripod_Y.step_num;     
    Tripod_Y.is_running = true;                 // Y轴运动标志位拉高
    __HAL_TIM_CLEAR_IT(&htim2, TIM_IT_UPDATE);  // 清除溢出中断标志位
    __HAL_TIM_SET_PRESCALER(&htim2, 72 * 5);
    HAL_TIM_Base_Start_IT(&htim2);              // 以中断方式开启时钟
}
```

### 3.4 绝对位置控制函数

```c
// X轴绝对位置控制
void Tripod_X_Abs_Step(int16_t X_Angle)
{
    uint16_t step;
    
    if (X_Angle > Tripod_X.angle)     // 如果大于上次角度，则向左转
    {
        Step_Motor_X_Dir(0); // 左转
        step = (X_Angle - Tripod_X.angle);     // 移动步数应该为两次之差
        Tripod_X_Step(step);
    }
    else if (X_Angle < Tripod_X.angle)  // 如果小于上次角度，则向右转
    {
        Step_Motor_X_Dir(1);
        step = (Tripod_X.angle - X_Angle);
        Tripod_X_Step(step);
    }
        
    Tripod_X.angle = X_Angle;  // 更新当前角度
}

// Y轴绝对位置控制
void Tripod_Y_Abs_Step(int16_t Y_Angle)
{
    uint16_t step;
    
    if (Y_Angle > Tripod_Y.angle)     // 如果大于上次角度，则向上转
    {
        Step_Motor_Y_Dir(1); // 向上转
        step = (Y_Angle - Tripod_Y.angle);     // 移动步数应该为两次之差
        Tripod_Y_Step(step);
    }
    else if (Y_Angle < Tripod_Y.angle)  // 如果小于上次角度，则向下转
    {
        Step_Motor_Y_Dir(0); // 向下转
        step = (Tripod_Y.angle - Y_Angle);
        Tripod_Y_Step(step);
    }

    Tripod_Y.angle = Y_Angle;  // 更新当前角度
}
```

### 3.5 角度控制函数

```c
// X轴角度控制
void Tripod_X_Angle(float X_Angle)
{
    uint16_t step;
    while(Tripod_X.is_running);  // 等待上一次运动完成
    
    if (X_Angle >= Tripod_X.angle)   // 大于目前角度
    {
        Step_Motor_X_Dir(0);   // 左转
        step = (X_Angle - Tripod_X.angle) * X_STEP_ANGLE;  // 获取角度差值，并乘以步进步数
        Tripod_X_Step(step);
    }
    else         // 小于目前角度
    {
        Step_Motor_X_Dir(1);   // 右转
        step = (Tripod_X.angle - X_Angle) * X_STEP_ANGLE;
        Tripod_X_Step(step);
    }
    Tripod_X.angle = X_Angle;   // 传递角度值
}

// Y轴角度控制
void Tripod_Y_Angle(float Y_Angle)
{
    uint16_t step;
    while(Tripod_Y.is_running);  // 等待上一次运动完成
    
    if (Y_Angle > Tripod_Y.angle)  // 目标角度大于当前角度
    {
        Step_Motor_Y_Dir(1);    // 向上转
        step = (Y_Angle - Tripod_Y.angle) * Y_STEP_ANGLE;
        Tripod_Y_Step(step);
    }
    else     // 小于当前角度
    {
        Step_Motor_Y_Dir(0);    // 向下转
        step = (Tripod_Y.angle - Y_Angle) * Y_STEP_ANGLE;
        Tripod_Y_Step(step);     
    }
    Tripod_Y.angle = Y_Angle;  // 更新当前角度
}
```

## 四、步进电机中断处理

```c
// TIM1中断处理函数 - X轴步进控制
void TIM1_UP_IRQHandler(void)
{
  HAL_TIM_IRQHandler(&htim1);
  
  HAL_GPIO_TogglePin(GPIOA, GPIO_PIN_2);  // 翻转电平，产生脉冲
  Tripod_X.step_num--;  // 步数减一
  
  // 加减速控制
  if(Tripod_X.step_code > 144)  // 步数大于144时使用加减速
  {
    if(Tripod_X.step_num > Tripod_X.step_code/2)  // 前半程加速
    {
        __HAL_TIM_SET_PRESCALER(&htim1, (72*5) - (Tripod_X.step_code - Tripod_X.step_num)*4);
    }
    else  // 后半程减速
    {
        __HAL_TIM_SET_PRESCALER(&htim1, (72*5) - (Tripod_X.step_code/2)*4 + (Tripod_X.step_code - Tripod_X.step_num)*4);        
    }
  }
  else
  {
      if((Tripod_X.step_code - Tripod_X.step_num) < 72) /* 启动时加速 */
      {
        __HAL_TIM_SET_PRESCALER(&htim1, (72*5) - (Tripod_X.step_code - Tripod_X.step_num)*4);
      }
      else if (Tripod_X.step_num < 72) /* 到达减速阶段 */
      {
        __HAL_TIM_SET_PRESCALER(&htim1, 72 + (72 - Tripod_X.step_num)*4);
      }
      // 两个条件都不满足时，做匀速运动，此时的预分频值为72
  }
    
  if(Tripod_X.step_num == 0) // 当指定个数脉冲输出完成之后
  {
    HAL_TIM_Base_Stop_IT(&htim1);  // 关闭定时器1
    Tripod_X.is_running = false;   // X轴运动标志位清除    
    __HAL_TIM_CLEAR_IT(&htim1, TIM_IT_UPDATE); // 清除定时器1计数溢出中断标志
  }
}

// TIM2中断处理函数 - Y轴步进控制
// 与X轴类似，略
```

## 五、步进电机控制流程

### 5.1 电机初始化流程

```c
void servo_system_init(void)
{
    Step_Motor_X_Cmd();    // 使能X轴电机
    Step_Motor_Y_Cmd();    // 使能Y轴电机
    Step_Motor_X_Dir(1);   // 控制方向  0 - 左转   1 - 右转
    Step_Motor_Y_Dir(1);   // 控制方向  0 - 下转   1 - 上转
}
```

### 5.2 串口指令到电机控制流程

```
1. 接收串口指令 (例如: "X+123")
2. 解析指令类型 (X轴控制)
3. 解析方向 ('+' 表示左转)
4. 解析步数 (123步)
5. 设置电机方向 (Step_Motor_X_Dir(0))
6. 执行绝对位置移动 (Tripod_X_Abs_Step(123))
7. 定时器中断控制电机脉冲输出
8. 完成指定步数后停止
```

### 5.3 加减速控制算法

系统采用S型加减速曲线控制步进电机，通过动态调整定时器预分频值实现:

1. **启动阶段**: 逐渐减小预分频值，提高脉冲频率，实现加速
2. **中间阶段**: 保持预分频值不变，维持匀速运动
3. **结束阶段**: 逐渐增大预分频值，降低脉冲频率，实现减速

加减速算法根据步数多少采用不同策略:
- 步数大于144: 使用完整的S型加减速曲线
- 步数较少: 使用简化的加减速曲线

## 六、步进电机控制优化建议

1. **平滑加减速**: 优化加减速曲线，使用更平滑的S型曲线，减少机械冲击
2. **微步控制**: 增加微步驱动功能，提高定位精度
3. **位置反馈**: 增加编码器反馈，实现闭环控制
4. **自动校准**: 增加原点自动校准功能，提高绝对位置精度
5. **电流控制**: 增加电机电流动态调整，减少发热和能耗
6. **防抖处理**: 增加电机停止时的防抖处理，提高定位精度
