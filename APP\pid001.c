
#include "pid_controller.h"
#include "bsp_system.h"


// 初始化PID控制器
void PID_Init(PID_Controller *pid, float Kp, float Ki, float Kd, float min, float max) {
    pid->Kp = Kp;
    pid->Ki = Ki;
    pid->Kd = Kd;
    pid->integral = 0.0f;
    pid->prev_error = 0.0f;
    pid->output_min = min;
    pid->output_max = max;
    pid->first_run = true;
}

// 计算PID输出（位置式算法）
float PID_Calculate(PID_Controller *pid, float setpoint, float measured_value) {
    // 计算当前误差
    float error = setpoint - measured_value;
    
    // 比例项
    float proportional = pid->Kp * error;
    
    // 积分项（带抗饱和）
    pid->integral += error;
    float integral = pid->Ki * pid->integral;
    
    // 微分项（如果是首次运行则跳过微分计算）
    float derivative = 0.0f;
    if (!pid->first_run) {
        derivative = pid->Kd * (error - pid->prev_error);
    } else {
        pid->first_run = false;
    }
    
    // 保存当前误差供下次使用
    pid->prev_error = error;
    
    // 计算总输出
    float output = proportional + integral + derivative;
    
    // 输出限幅（确保在舵机角度范围内）
    if (output > pid->output_max) {
        output = pid->output_max;
        // 抗积分饱和：当输出饱和时停止积分累积
        pid->integral -= error;
    } else if (output < pid->output_min) {
        output = pid->output_min;
        pid->integral -= error;
    }
    
    return output;
}



#define X_SERVO_TIMER     &htim2    // X轴舵机使用的定时器
#define X_SERVO_CHANNEL    TIM_CHANNEL_1

#define Y_SERVO_TIMER     &htim3    // Y轴舵机使用的定时器
#define Y_SERVO_CHANNEL    TIM_CHANNEL_1

// 全局PID控制器实例
PID_Controller pid_x, pid_y;

// 角度转PWM占空比函数
// 舵机参数：0.5ms(0度) ~ 2.5ms(180/270度)，周期20ms(50Hz)
//uint32_t angle_to_pulse(float angle, bool is_x_axis) {
//    // 确定角度范围
//    float min_angle = 0.0f;
//    float max_angle = is_x_axis ? 270.0f : 180.0f;
//    
//    // 限幅保护舵机
//    if (angle < min_angle) angle = min_angle;
//    if (angle > max_angle) angle = max_angle;
//    
//    // 计算脉冲宽度（单位：us）
//    float pulse_us;
//    if (is_x_axis) {
//        pulse_us = 500.0f + (angle / 270.0f) * 2000.0f; // X轴：0-270度转500-2500us
//    } else {
//        pulse_us = 500.0f + (angle / 180.0f) * 2000.0f; // Y轴：0-180度转500-2500us
//    }
//    
//    // 将脉冲宽度转换为定时器计数值（假设定时器时钟为72MHz，预分频72，则每微秒1个计数）
//    return (uint32_t)(pulse_us);  // 1us = 1 tick
//}

// 设置舵机角度
void set_servo_angle(TIM_HandleTypeDef *htim, uint32_t channel, uint32_t pulse) {
    // 保护舵机：确保脉冲在安全范围内
    if (pulse < 500) pulse = 500;
    if (pulse > 2500) pulse = 2500;
    
    // 设置捕获比较寄存器值
    switch (channel) {
        case TIM_CHANNEL_1:
            htim->Instance->CCR1 = pulse;
            break;
        case TIM_CHANNEL_2:
            htim->Instance->CCR2 = pulse;
            break;
        case TIM_CHANNEL_3:
            htim->Instance->CCR3 = pulse;
            break;
        case TIM_CHANNEL_4:
            htim->Instance->CCR4 = pulse;
            break;
        default:
            break;
    }
}

// 获取当前角度（模拟函数，实际需根据传感器实现）
float get_current_angle_x(void) {
    // 实际使用时替换为真实传感器读取代码
    // 这里返回一个模拟值（例如从电位器ADC读取）
    return 135.0f;  // 示例值
}

float get_current_angle_y(void) {
    // 实际使用时替换为真实传感器读取代码
    return 90.0f;   // 示例值
}






















