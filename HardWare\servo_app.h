#ifndef __SERVO_APP_H
#define __SERVO_APP_H

#include "bsp_system.h"

#define X_STEP_ANGLE    17.78f    //云台X轴运行一度需要的步数
#define Y_STEP_ANGLE    17.78f    //云台Y轴运行一度需要的步数


typedef struct Tripod_Str  //云台相关结构体
{
    volatile uint32_t step_num;  //需要运行的步数
    volatile uint32_t step_code;
    //uint16_t coord;  //当前坐标
    float angle;  //当前角度
    volatile bool is_limit;  //是否到达限位
    volatile bool is_running;  //是否正在运行 （volatile标记这是一个不能优化的变量）
}Tripod_Struct;

extern Tripod_Struct Tripod_X, Tripod_Y;

// 插补控制结构体
typedef struct Interpolation_Str
{
    int16_t target_x;          // X轴目标位置
    int16_t target_y;          // Y轴目标位置
    int16_t current_x;         // X轴当前位置
    int16_t current_y;         // Y轴当前位置
    int16_t delta_x;           // X轴总移动距离
    int16_t delta_y;           // Y轴总移动距离
    int16_t abs_delta_x;       // X轴绝对移动距离
    int16_t abs_delta_y;       // Y轴绝对移动距离
    int16_t error;             // Bresenham算法误差累积器
    uint16_t total_steps;      // 总步数
    uint16_t current_step;     // 当前步数
    uint8_t x_dir;             // X轴方向
    uint8_t y_dir;             // Y轴方向
    bool is_interpolating;     // 插补运行标志
}Interpolation_Struct;
	
extern Interpolation_Struct interp;


void Step_Motor_X_Cmd(void);			//X轴电机使用或关闭
void Step_Motor_X_DisCmd(void);
void Step_Motor_Y_Cmd(void);			//Y轴电机使用或关闭
void Step_Motor_Y_DisCmd(void);

void Step_Motor_X_Dir(uint8_t x); 	//X轴电机转动方向控制
void Step_Motor_Y_Dir(uint8_t x);		    //Y轴电机转动方向控制

/*设置X轴步进步数*/
void Tripod_X_Step(uint16_t num);          //相对转动
/*设置Y轴步进步数*/
void Tripod_Y_Step(uint16_t num); 		   //相对转动
/*X轴和Y轴左右转动*/
void Tripod_X_Abs_Step(int16_t num);         //X轴转动 --- 绝对转动
void Tripod_Y_Abs_Step(int16_t num);         //Y轴转动 --- 绝对转动
/*设置X轴转动角度*/
void Tripod_X_Angle(float X_Angle);
/*设置Y轴转动角度*/
void Tripod_Y_Angle(float Y_Angle);


void Interpolation_Step(void);
// 主插补函数
void Tripod_Interpolation_Move(int16_t target_x, int16_t target_y);
// 初始化
void Interpolation_Init(void);

/*云台PID测试*/
void Tripod_PID_Text(void);

/*任务一PID*/
void Tripod_PID_Task1(void);

// 添加一个新函数，结合插补和PID
void Tripod_PID_Interpolation_Line(int16_t points[][2], uint8_t num_points);
#endif

