# 2023年电赛E题完整技术方案解析

## 🎯 **赛题理解与系统架构**

### **赛题核心任务**
这是一个**激光循迹云台系统**，主要任务是：
1. **任务1**: 激光回到中心点（定点瞄准）
2. **任务2**: 激光沿四个点围绕一圈（轨迹跟踪）  
3. **任务3**: 激光循迹黑胶布（线条跟踪）

### **系统架构图**
```
上位机PC ←UART1→ STM32主控 ←UART3→ OpenMV视觉模块
                    ↓
                二轴云台系统
                    ↓
                激光发射器
```

---

## 🔧 **关键技术疑问详解**

### **1. Step（步数）的含义和单位**

#### **Step不是摄像头的量，而是步进电机的物理单位**
```c
// Step的物理含义
int16_t X_Step;  // X轴步进电机的步数
int16_t Y_Step;  // Y轴步进电机的步数

// 1步 = 步进电机转动的最小角度单位
// 例如：1.8°步进电机，1步 = 1.8°
```

#### **Step与像素坐标的转换关系**
```c
// 摄像头坐标系 → 步进电机步数的转换
current_x = 123;  // OpenMV反馈的像素坐标X=123
X_Step = 123;     // 转换为步进电机的123步

// 这里的转换比例是 1:1，即1个像素对应1步
// 实际项目中这个比例需要通过标定确定
```

### **2. X_STEP_ANGLE（17.78）的确定方法**

#### **角度步数常量的物理意义**
```c
#define X_STEP_ANGLE    17.78f    // 云台X轴运行一度需要的步数
#define Y_STEP_ANGLE    17.78f    // 云台Y轴运行一度需要的步数

// 计算方法：
// 假设步进电机：1.8°/步，减速比1:10
// 则云台转动1°需要的步数 = (1° / 1.8°) × 10 = 5.56步
// 17.78可能是考虑了更复杂的机械传动比
```

#### **标定方法**
```c
// 赛前标定流程：
// 1. 发送角度控制指令
void calibrate_step_angle() {
    Tripod_X_Angle(10.0f);  // 让云台转动10度
    // 2. 测量实际转动角度
    // 3. 计算：X_STEP_ANGLE = 实际步数 / 实际角度
}
```

### **3. 视觉方案详解**

#### **OpenMV视觉识别流程**
```python
# OpenMV端的典型代码逻辑
import sensor, image, time, pyb
from pyb import UART

# 初始化摄像头
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)

uart = UART(3, 115200)  # 与STM32的UART3通信

while True:
    img = sensor.snapshot()
    
    # 1. 识别黑线（任务3）
    line = img.find_lines(threshold=1000, theta_margin=25, rho_margin=25)
    
    # 2. 识别矩形框（任务2）
    rectangles = img.find_rectangles(threshold=10000)
    
    # 3. 发送坐标给STM32
    if line:
        uart.write("GDx{:03d}y{:03d}".format(line.x(), line.y()))
    
    if rectangles:
        # 发送矩形四个顶点坐标
        uart.write("JX x{:03d}y{:03d} x{:03d}y{:03d}...".format(...))
```

#### **矩形顶点坐标的获取和存储**
```c
// STM32端接收矩形坐标
int16_t PID_text[4][2] = {0};  // 存储矩形四个顶点

// 解析OpenMV发送的矩形数据："JX x123y456 x789y012 x345y678 x901y234"
void parse_rectangle_data() {
    // PID_text[0][0/1] - 左上点X/Y坐标
    // PID_text[1][0/1] - 右上点X/Y坐标  
    // PID_text[2][0/1] - 右下点X/Y坐标
    // PID_text[3][0/1] - 左下点X/Y坐标
}
```

### **4. 绕矩形移动的实现方案**

#### **任务3的循迹算法**
```c
void Tripod_PID_Text(void) {
    static uint8_t PID_text_index = 0;  // 当前目标点索引
    
    // 计算到当前目标点的误差
    float x_error = PID_text[PID_text_index % 4][0] - current_x;
    float y_error = PID_text[PID_text_index % 4][1] - current_y;
    
    // PID控制到达目标点
    float x_adjustment = PID_Calculate(&pid_x, current_x, PID_text[PID_text_index % 4][0]);
    float y_adjustment = PID_Calculate(&pid_y, current_y, PID_text[PID_text_index % 4][1]);
    
    // 到达当前点后，切换到下一个点
    if ((abs(x_error) <= 4) && (abs(y_error) <= 4)) {
        if (++stable_count > 20) {  // 稳定计数，确保真正到达
            stable_count = 0;
            if (++PID_text_index == 5) {  // 完成一圈后停止
                PID_text_index = 0;
                Task3_Start = false;
            }
        }
    }
}
```

#### **移动路径**
```
起点 → 左上点 → 右上点 → 右下点 → 左下点 → 回到起点 → 结束
```

### **5. PID控制系统详解**

#### **PID的作用和控制对象**
```c
// PID控制的是：像素坐标误差 → 云台角度调整
PID_Controller pid_x, pid_y;  // X轴和Y轴各有独立的PID控制器

// PID控制流程：
// 1. 计算像素误差
float x_error = target_x - current_x;  // 目标像素 - 当前像素

// 2. PID计算输出调整量
float x_adjustment = PID_Calculate(&pid_x, current_x, target_x);

// 3. 转换为角度调整
float x_angle_change = x_adjustment * 0.07f;  // 调整系数

// 4. 应用到云台角度
Tripod_X_Angle(Tripod_X.angle + x_angle_change);
```

#### **PID参数含义**
```c
// PID初始化参数
PID_Init(&pid_x, 0.2, 0, 0, 20, 500);
//           Kp   Ki Kd  积分限幅 输出限幅

// Kp=0.2: 比例系数，误差越大，调整越大
// Ki=0:   积分系数，消除稳态误差（这里设为0）
// Kd=0:   微分系数，减少超调（这里设为0）
```

### **6. 激光控制方案**

#### **激光器控制**
```c
// 激光器控制非常简单
void Laser_ON(void) {
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12, GPIO_PIN_SET);  // 高电平开启
}

void Laser_OFF(void) {
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12, GPIO_PIN_RESET); // 低电平关闭
}

// 主循环中激光始终开启
while (1) {
    // 执行各种任务...
    Laser_ON();  // 激光始终开启
}
```

#### **激光跟踪原理**
激光不是"移动"的，而是**云台带着激光器移动**：
1. OpenMV识别黑线中心点坐标
2. STM32控制云台转动，使激光指向该坐标
3. 激光始终开启，跟随云台指向目标

### **7. 插补算法详解**

#### **Bresenham直线插补算法**
```c
// 插补的作用：在两点间生成平滑的移动轨迹
void Tripod_Interpolation_Move(int16_t target_x, int16_t target_y) {
    // 计算移动距离
    interp.delta_x = target_x - current_x;
    interp.delta_y = target_y - current_y;
    
    // 确定主轴（移动距离更大的轴）
    interp.total_steps = max(abs(delta_x), abs(delta_y));
    
    // 启动TIM3定时器，定期执行插补步进
    HAL_TIM_Base_Start_IT(&htim3);
}

// TIM3中断中执行单步插补
void Interpolation_Step(void) {
    // Bresenham算法决定下一步移动方向
    // 每次移动1个像素单位，直到到达目标点
}
```

---

## 🔄 **完整工作流程**

### **赛前标定阶段**
```bash
# 1. 连接上位机，启动串口调试
# 2. 机械零点校准
发送: "X+000" "Y+000"  # 回到机械零点
发送: "A"              # 记录零点

# 3. 工作区域标定  
发送: "X+200" "A"      # 记录右边界
发送: "X-400" "A"      # 记录左边界
发送: "Y+150" "A"      # 记录上边界
发送: "Y-300" "A"      # 记录下边界

# 4. 视觉系统测试
发送: "OK"             # 请求OpenMV坐标
接收: "GDx123y456"     # OpenMV反馈坐标
```

### **比赛演示阶段**
```bash
# 评委计时开始
发送: "S1"  # 任务1：激光回中心点
# 系统自动执行：Tripod_PID_Task1() → 激光指向Task1_step[4]坐标

发送: "S2"  # 任务2：四点围绕
# 系统自动执行：Tripod_PID_Interpolation_Line() → 按顺序访问4个点

发送: "S3"  # 任务3：循迹黑胶布  
# 系统自动执行：Tripod_PID_Text() → OpenMV识别黑线，激光跟踪
```

### **关键参数传递链路**
```
OpenMV识别 → UART3发送坐标 → STM32接收current_x/y → 
PID计算误差 → 角度调整量 → 步进电机控制 → 云台转动 → 激光指向目标
```

---

## 📊 **关键参数解算表**

### **坐标转换关系**
| 参数 | 含义 | 单位 | 转换关系 |
|------|------|------|----------|
| `current_x/y` | OpenMV像素坐标 | 像素 | 0-320 (QVGA分辨率) |
| `X_Step/Y_Step` | 步进电机步数 | 步 | 1像素 = 1步 (1:1映射) |
| `Tripod_X/Y.angle` | 云台角度 | 步 | 角度制步数，非度数 |
| `X_STEP_ANGLE` | 角度步数常量 | 步/度 | 17.78步/度 |

### **PID控制参数**
| 参数 | X轴PID | Y轴PID | 作用 |
|------|--------|--------|------|
| Kp | 0.2 | 0.2 | 比例系数，响应速度 |
| Ki | 0 | 0 | 积分系数，消除稳态误差 |
| Kd | 0 | 0 | 微分系数，减少超调 |
| 调整系数 | 0.07f | 0.07f | PID输出→角度转换系数 |

### **定时器配置**
| 定时器 | 功能 | 预分频 | 周期 | 频率 |
|--------|------|--------|------|------|
| TIM1 | X轴步进脉冲 | 72×5 | 动态 | 可变(加减速) |
| TIM2 | Y轴步进脉冲 | 72×5 | 动态 | 可变(加减速) |
| TIM3 | 插补算法 | 720-1 | 1000-1 | 100Hz |

### **任务执行逻辑**
| 任务 | 触发指令 | 目标坐标 | 控制算法 | 结束条件 |
|------|----------|----------|----------|----------|
| 任务1 | "S1" | Task1_step[4] | Tripod_PID_Task1() | 到达中心点 |
| 任务2 | "S2" | Task1_step[0-3] | 插补+PID | 完成四点循环 |
| 任务3 | "S3" | PID_text[0-3] | Tripod_PID_Text() | 完成矩形循迹 |

---

## 🎯 **核心技术总结**

### **系统特点**
1. **双串口架构**: UART1调试 + UART3视觉通信
2. **闭环控制**: 视觉反馈 + PID控制 + 步进执行
3. **多任务管理**: 三种不同的运动模式
4. **实时插补**: Bresenham算法实现平滑轨迹
5. **加减速控制**: S型曲线减少机械冲击

### **关键算法**
- **PID控制**: 像素误差 → 角度调整
- **插补算法**: 两点间平滑移动
- **加减速**: 动态调整定时器预分频
- **坐标转换**: 像素坐标 ↔ 步进步数

### **成功要素**
1. **精确标定**: 赛前确定坐标转换关系
2. **稳定视觉**: OpenMV识别算法优化
3. **平滑控制**: PID参数调优
4. **可靠通信**: 串口数据校验和容错

这就是2023年电赛E题的完整技术实现方案！核心是**视觉识别+PID控制+步进电机驱动**的闭环控制系统。
