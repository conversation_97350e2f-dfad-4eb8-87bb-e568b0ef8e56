# 串口通信指令协议表

## UART1 上位机通信协议

| 指令格式 | 示例 | 功能描述 | 参数说明 | 返回值 |
|---------|------|---------|---------|-------|
| `X±ddd` | `X+123` | X轴步进电机控制 | ±: 方向(+左转/-右转)<br>ddd: 3位数字步数(000-999) | `X_Step = 123, Y_Step = 0` |
| `Y±ddd` | `Y-045` | Y轴步进电机控制 | ±: 方向(+上转/-下转)<br>ddd: 3位数字步数(000-999) | `X_Step = 0, Y_Step = -45` |
| `OK` | `OK` | 确认当前位置 | 无 | `OK:: X_Step = 123, Y_Step = 45` |
| `A` | `A` | 记录当前点位 | 无 | `OK:: X_Step = 123, Y_Step = 45` |
| `S1` | `S1` | 启动任务1 | 无 | `Starting Task 1 !!!` |
| `S2` | `S2` | 启动任务2 | 无 | `Starting Task 2 !!!` |
| `S3` | `S3` | 启动任务3 | 无 | `Starting Task 3 !!!` |
| `T` | `T` | 停止所有任务 | 无 | `Stoping` |
| `FHT` | `FHT` | 发挥题暂停 | 无 | 无 |

## UART3 OpenMV通信协议

### 接收指令格式

| 指令格式 | 示例 | 功能描述 | 参数说明 |
|---------|------|---------|---------|
| `GDxdddyddd` | `GDx123y456` | 获取当前坐标 | x后3位: X坐标(000-999)<br>y后3位: Y坐标(000-999) |
| `JX xdddyddd xdddyddd xdddyddd xdddyddd` | `JX x123y456 x789y012 x345y678 x901y234` | 获取矩形框四个顶点坐标 | 四组坐标分别为:<br>左上点、右上点、右下点、左下点 |

### 发送指令格式

| 指令 | 功能描述 | 触发条件 |
|-----|---------|---------|
| `JX` | 请求矩形框坐标 | 收到上位机`OK`指令 |
| `REST` | 复位OpenMV | 收到上位机`S1`指令 |
| `S3` | 启动任务3 | 收到上位机`S3`指令 |

## 数据解析方法

### ASCII码转数字算法
```c
// 将ASCII字符'0'-'9'(十进制48-57)转换为数字0-9
int value = (ascii_char - 48);

// 三位数字解析示例: "123" -> 123
int number = (buf[0] - 48) * 100 + (buf[1] - 48) * 10 + (buf[2] - 48);
```

### 坐标数据存储结构

#### 任务点位存储
```c
// 存储最多5个点的X,Y坐标
int16_t Task1_step[5][2] = {0};

// 存储示例:
// Task1_step[0][0] = 123;  // 第1个点的X坐标
// Task1_step[0][1] = 456;  // 第1个点的Y坐标
// Task1_step[1][0] = 789;  // 第2个点的X坐标
// Task1_step[1][1] = 012;  // 第2个点的Y坐标
```

#### 矩形框坐标存储
```c
// 存储矩形框四个顶点坐标
int16_t PID_text[4][2];

// 存储示例:
// PID_text[0][0/1] - 左上点X/Y坐标
// PID_text[1][0/1] - 右上点X/Y坐标
// PID_text[2][0/1] - 右下点X/Y坐标
// PID_text[3][0/1] - 左下点X/Y坐标
```

## 通信状态标志

| 变量名 | 类型 | 功能描述 | 状态说明 |
|-------|------|---------|---------|
| `uart1_rx_flag` | bool | UART1接收完成标志 | true: 数据接收完成，待处理<br>false: 无新数据或已处理完成 |
| `uart3_rx_flag` | bool | UART3接收完成标志 | true: 数据接收完成，待处理<br>false: 无新数据或已处理完成 |
| `Task1_Start` | bool | 任务1启动标志 | true: 任务1运行中<br>false: 任务1未启动或已停止 |
| `Task2_Start` | bool | 任务2启动标志 | true: 任务2运行中<br>false: 任务2未启动或已停止 |
| `Task3_Start` | bool | 任务3启动标志 | true: 任务3运行中<br>false: 任务3未启动或已停止 |
| `Task_Stop_Plas` | bool | 发挥题暂停标志 | true: 暂停状态<br>false: 正常运行状态 |

## 缓冲区管理

| 缓冲区名称 | 大小 | 功能描述 | 使用方式 |
|-----------|-----|---------|---------|
| `uart1_rx_dma_buf` | 128字节 | UART1 DMA接收缓冲区 | DMA硬件自动填充，中断后复制到处理缓冲区 |
| `uart3_rx_dma_buf` | 128字节 | UART3 DMA接收缓冲区 | DMA硬件自动填充，中断后复制到处理缓冲区 |
| `uart1_rx_buf` | 128字节 | UART1数据处理缓冲区 | 软件解析指令，处理完成后清空 |
| `uart3_rx_buf` | 128字节 | UART3数据处理缓冲区 | 软件解析指令，处理完成后清空 |

## 注意事项

1. **数字解析**: 所有数字均采用ASCII码转换，字符'0'-'9'的ASCII码为48-57，需减去48得到实际数值
2. **缓冲区清理**: 每次处理完数据后必须调用`memset()`清空缓冲区，防止数据残留影响下次解析
3. **标志位重置**: 处理完数据后必须重置对应的接收标志位(`uart1_rx_flag = false`)
4. **DMA接收**: 系统使用DMA接收模式，接收完成后自动触发`HAL_UARTEx_RxEventCallback`回调
5. **方向控制**: X轴方向: 0-左转, 1-右转; Y轴方向: 1-上转, 0-下转
6. **坐标系统**: 系统使用相对坐标系，通过`current_x`和`current_y`记录当前位置
