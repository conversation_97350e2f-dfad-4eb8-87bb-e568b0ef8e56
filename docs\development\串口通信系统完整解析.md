# 串口通信系统完整解析

## 系统概述
这是一个基于STM32F103的双串口通信系统，用于控制二轴云台步进电机。系统通过UART1接收上位机控制指令，通过UART3与OpenMV视觉模块通信。

## 一、关键变量详解

### 1.1 串口缓冲区变量
```c
uint8_t uart1_rx_dma_buf[128];    // UART1 DMA接收缓冲区
uint8_t uart3_rx_dma_buf[128];    // UART3 DMA接收缓冲区  
uint8_t uart1_rx_buf[128];        // UART1 数据处理缓冲区
uint8_t uart3_rx_buf[128];        // UART3 数据处理缓冲区
bool uart1_rx_flag = false;       // UART1 接收完成标志
bool uart3_rx_flag = false;       // UART3 接收完成标志
```

### 1.2 步进电机控制变量
```c
int16_t X_Step;                   // X轴步进值（带符号）
int16_t Y_Step;                   // Y轴步进值（带符号）
int16_t current_x, current_y;     // 当前X、Y坐标位置
```

### 1.3 任务控制变量
```c
bool Task1_Start = false;         // 任务一启动标志
bool Task2_Start = false;         // 任务二启动标志  
bool Task3_Start = false;         // 任务三启动标志
bool Task_Stop_Plas = false;      // 发挥题暂停标志
uint8_t Task_Stop_num = 0;        // 暂停次数计数
```

### 1.4 点位存储变量
```c
int16_t Task1_step[5][2] = {0};   // 任务一点位存储数组[5个点][X,Y坐标]
uint8_t i = 0;                    // 点位存储索引
int16_t PID_text[4][2];           // 矩形框四个顶点坐标存储
```

## 二、核心函数解析

### 2.1 串口接收中断回调函数
```c
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
```
**功能**: DMA接收完成时自动调用
**执行流程**:
1. 判断是UART1还是UART3
2. 将DMA缓冲区数据复制到处理缓冲区
3. 设置接收完成标志
4. 清空DMA缓冲区，准备下次接收

### 2.2 UART1任务处理函数
```c
void uart_task(void)
```
**功能**: 处理上位机发送的控制指令
**支持的指令格式**:
- `X+123` / `X-123`: X轴正向/负向移动123步
- `Y+123` / `Y-123`: Y轴正向/负向移动123步  
- `OK`: 确认指令，向UART3发送"JX"
- `A`: 记录当前坐标到任务点位数组
- `S1` / `S2` / `S3`: 启动任务1/2/3
- `T`: 停止所有任务
- `FHT`: 发挥题暂停

### 2.3 UART3任务处理函数
```c
void uart3_task(void)
```
**功能**: 处理OpenMV视觉模块数据
**支持的数据格式**:
- `GDx123y456`: 获取当前坐标位置
- `JX x123y456 x789y012 x345y678 x901y234`: 获取矩形框四个顶点坐标

## 三、执行流程详解

### 3.1 系统初始化流程
1. **硬件初始化**: 配置UART1、UART3、DMA、GPIO
2. **变量初始化**: 清零所有缓冲区和标志位
3. **电机初始化**: 调用`servo_system_init()`使能步进电机
4. **中断使能**: 启动DMA接收中断

### 3.2 数据接收流程
```
数据到达 → DMA自动接收 → 触发RxEventCallback中断 → 
数据复制到处理缓冲区 → 设置接收标志 → 主循环检测标志 → 
调用对应任务处理函数 → 解析指令 → 执行相应动作
```

### 3.3 指令解析流程

#### X/Y轴控制指令解析
```c
// 示例: "X+123"
if (uart1_rx_buf[0] == 'x' || uart1_rx_buf[0] == 'X') {
    // 1. 提取步数: (buf[2]-48)*100 + (buf[3]-48)*10 + (buf[4]-48)
    X_Step = (uart1_rx_buf[2] - 48) * 100 + (uart1_rx_buf[3] - 48) * 10 + (uart1_rx_buf[4] - 48);
    
    // 2. 判断方向
    if(uart1_rx_buf[1] == '+') {
        Step_Motor_X_Dir(0);  // 左转
    } else if (uart1_rx_buf[1] == '-') {
        Step_Motor_X_Dir(1);  // 右转  
        X_Step = -X_Step;     // 负值标记
    }
    
    // 3. 执行绝对位置移动
    Tripod_X_Abs_Step(X_Step);
}
```

#### 坐标数据解析流程
```c
// 示例: "GDx123y456"
if (uart3_rx_buf[0] == 'G' && uart3_rx_buf[1] == 'D') {
    // 解析X坐标: 位置3-5
    if (uart3_rx_buf[2] == 'x') {
        current_x = (uart3_rx_buf[3] - 48) * 100 + 
                   (uart3_rx_buf[4] - 48) * 10 + 
                   (uart3_rx_buf[5] - 48);
    }
    // 解析Y坐标: 位置6-9  
    if (uart3_rx_buf[6] == 'y') {
        current_y = (uart3_rx_buf[7] - 48) * 100 + 
                   (uart3_rx_buf[8] - 48) * 10 + 
                   (uart3_rx_buf[9] - 48);
    }
}
```

### 3.4 步进电机控制流程

#### 绝对位置移动算法
```c
void Tripod_X_Abs_Step(int16_t X_Angle) {
    uint16_t step;
    
    if (X_Angle > Tripod_X.angle) {      // 目标位置大于当前位置
        Step_Motor_X_Dir(0);             // 设置左转方向
        step = (X_Angle - Tripod_X.angle); // 计算步数差
        Tripod_X_Step(step);             // 执行步进
    } else if (X_Angle < Tripod_X.angle) { // 目标位置小于当前位置
        Step_Motor_X_Dir(1);             // 设置右转方向  
        step = (Tripod_X.angle - X_Angle); // 计算步数差
        Tripod_X_Step(step);             // 执行步进
    }
    
    Tripod_X.angle = X_Angle;            // 更新当前位置
}
```

## 四、数据流向图

```
上位机 ──UART1──> STM32 ──解析指令──> 步进电机控制
   ↑                ↓
   └─── 状态反馈 ←───┘

OpenMV ──UART3──> STM32 ──坐标数据──> 位置更新
   ↑                ↓  
   └─── 控制指令 ←───┘
```

## 五、关键技术要点

### 5.1 ASCII到数字转换
```c
// 字符'0'-'9'的ASCII码为48-57，减去48得到数字0-9
number = (buf[i] - 48) * 100 + (buf[i+1] - 48) * 10 + (buf[i+2] - 48);
```

### 5.2 DMA双缓冲机制
- DMA缓冲区: 硬件自动接收数据
- 处理缓冲区: 软件解析数据
- 避免数据覆盖和丢失

### 5.3 状态机设计
- 通过标志位控制任务状态
- 支持多任务并发控制
- 实现暂停/恢复功能

### 5.4 错误处理机制
- 缓冲区溢出保护
- 无效指令过滤  
- 通信超时检测

## 六、优化建议

1. **增加校验机制**: 添加CRC或校验和验证数据完整性
2. **指令队列**: 实现指令缓存队列，提高响应速度
3. **错误恢复**: 增加通信错误自动恢复机制
4. **参数配置**: 将硬编码参数改为可配置参数
5. **日志记录**: 添加操作日志记录功能
