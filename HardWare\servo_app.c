#include "servo_app.h"
#include "tim.h"
#include "math.h"

Interpolation_Struct interp = {0};

int16_t current_x, current_y;
bool step_Error_Flag = false;  	//步数误差

void Step_Motor_X_Cmd(void)
{
	HAL_GPIO_WritePin(GPIOA, GPIO_PIN_0, GPIO_PIN_SET);        //X轴使能引脚
}

void Step_Motor_X_DisCmd(void)
{
	HAL_GPIO_WritePin(GPIOA, GPIO_PIN_0, GPIO_PIN_RESET);        //X轴使能引脚
}

void Step_Motor_Y_Cmd(void)
{
	HAL_GPIO_WritePin(GPIOA, GPIO_PIN_3, GPIO_PIN_SET);        //Y轴使能引脚
}

void Step_Motor_Y_DisCmd(void)
{
	HAL_GPIO_WritePin(GPIOA, GPIO_PIN_3, GPIO_PIN_RESET);        //Y轴使能引脚
}

void Step_Motor_X_Dir(uint8_t x)
{
	HAL_GPIO_WritePin(GPIOA, GPIO_PIN_1, (GPIO_PinState) x);        //X轴电机转向控制
}

void Step_Motor_Y_Dir(uint8_t x)
{
	HAL_GPIO_WritePin(GPIOA, GPIO_PIN_4, (GPIO_PinState) x);        //X轴电机转向控制
}



/*设置X轴步进步数*/
void Tripod_X_Step(uint16_t num)
{
    if(num == 0)
        return;
	
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_2, GPIO_PIN_RESET);
    Tripod_X.step_num = num * 2;
    Tripod_X.step_code = Tripod_X.step_num;     //转存步数
	Tripod_X.is_running = true;					//X轴运动标志位拉高
    __HAL_TIM_CLEAR_IT(&htim1, TIM_IT_UPDATE);  //清除溢出中断标志位
	__HAL_TIM_SET_PRESCALER(&htim1, 72 * 5);
    HAL_TIM_Base_Start_IT(&htim1);    			//以中断方式开启时钟
}

/*设置Y轴步进步数*/
void Tripod_Y_Step(uint16_t num)
{
    if(num == 0)
        return;
	
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_5, GPIO_PIN_RESET);
    Tripod_Y.step_num = num * 2;
    Tripod_Y.step_code = Tripod_Y.step_num;		
	Tripod_Y.is_running = true;					  //Y轴运动标志位拉高
    __HAL_TIM_CLEAR_IT(&htim2, TIM_IT_UPDATE);    //清除溢出中断标志位
	__HAL_TIM_SET_PRESCALER(&htim2, 72 * 5);
    HAL_TIM_Base_Start_IT(&htim2);    			  //以中断方式开启时钟
}

/*X轴绝对转动*/
void Tripod_X_Abs_Step(int16_t X_Angle)
{
	uint16_t step;
//	while(Tripod_X.is_running);
	if (X_Angle > Tripod_X.angle)     //如果大于上次角度，则向左转
	{
		Step_Motor_X_Dir(0); //左转
		step = (X_Angle - Tripod_X.angle);     //移动步数应该为两次之差
		Tripod_X_Step(step);
	}
	else if (X_Angle < Tripod_X.angle)  //如果小于上次角度，则向右转
	{
		Step_Motor_X_Dir(1);
		step = (Tripod_X.angle - X_Angle);
		Tripod_X_Step(step);
	}
		
	Tripod_X.angle = X_Angle;
}

/*Y轴绝对转动*/
void Tripod_Y_Abs_Step(int16_t Y_Angle)
{
	uint16_t step;
//	while(Tripod_Y.is_running);
	if (Y_Angle > Tripod_Y.angle)     //如果大于上次角度，则向上转
	{
		Step_Motor_Y_Dir(1); //向上转
		step = (Y_Angle - Tripod_Y.angle);     //移动步数应该为两次之差
		Tripod_Y_Step(step);
	}
	else if (Y_Angle < Tripod_Y.angle)  //如果小于上次角度，则向下转
	{
		Step_Motor_Y_Dir(0); //向下转
		step = (Tripod_Y.angle - Y_Angle);
		Tripod_Y_Step(step);
	}

	Tripod_Y.angle = Y_Angle;
}


/*设置X轴转动角度*/
void Tripod_X_Angle(float X_Angle)
{
	uint16_t step;
	while(Tripod_X.is_running);
	if (X_Angle >= Tripod_X.angle)   //大于目前角度
	{
		Step_Motor_X_Dir(0);   //左转
		step = (X_Angle - Tripod_X.angle) * X_STEP_ANGLE;  //获取角度差值，并乘以步进步数
		Tripod_X_Step(step);
	}
	else         //小于目前角度
	{
		Step_Motor_X_Dir(1);   //右转
		step = (Tripod_X.angle - X_Angle) * X_STEP_ANGLE;
		Tripod_X_Step(step);
	}
	Tripod_X.angle = X_Angle;   //传递角度值
}

/*设置Y轴转动角度*/
void Tripod_Y_Angle(float Y_Angle)
{
	uint16_t step;
	while(Tripod_Y.is_running);
	if (Y_Angle > Tripod_Y.angle)  //目标角度大于当前角度
	{
		Step_Motor_Y_Dir(1);    //向上转
		step = (Y_Angle - Tripod_Y.angle) * Y_STEP_ANGLE;
		Tripod_Y_Step(step);
	}
	else     //小于当前角度
	{
		Step_Motor_Y_Dir(0);    //向下转
		step = (Tripod_Y.angle - Y_Angle) * Y_STEP_ANGLE;
		Tripod_Y_Step(step);		
	}
	Tripod_Y.angle = Y_Angle;
}



/*X轴坐标动作 --- 到达指定坐标*/
void Tripod_X_Coord(int X_Dat)
{
//	float temp;
//	
//	while(Tripod_X.is_running);
//	temp = (float)X_Dat;
//	temp = temp - X_OFFSET;			//因为没有固定的坐标系，所以需要根据摄像头识别内容确认X_OFFSET值
//	temp = atan2(temp, DIS_X);
}

/*Y轴坐标动作 --- 到达指定坐标*/
void Tripod_Y_Coord(int Y_Dat)
{

}

/* PID控制 */
void Tripod_PID_Goto(int16_t target_x, int16_t target_y, int16_t error)
{
    // 计算像素误差
	float x_error = interp.target_x - current_x;
	float y_error = interp.target_y - current_y;
//    float x_error = target_x - current_x;
//    float y_error = target_y - current_y;	
	
// 计算PID输出
    float x_adjustment = PID_Calculate(&pid_x, current_x, target_x);
    float y_adjustment = PID_Calculate(&pid_y, current_y, target_y);
	
    // 将PID输出转换为角度调整
    // 这里需要根据实际系统特性调整转换关系
    float x_angle_change = x_adjustment * 0.10f; // 调整系数
    float y_angle_change = y_adjustment * 0.10f;
    
    // 应用角度调整
    Tripod_X_Angle(Tripod_X.angle + x_angle_change);
    Tripod_Y_Angle(Tripod_Y.angle + y_angle_change);

	if ((abs((int)x_error) <= error) && (abs((int)y_error) <= error))
	{
		step_Error_Flag = true;   //标志位置位
	}
}

// 插补单步执行函数
void Interpolation_Step(void)
{
	int16_t next_x = 0;
	int16_t next_y = 0;
	
    if ((interp.current_step >= interp.total_steps) && (step_Error_Flag == true))    //如果当前步数大于总的步数，且步数误差在一定范围内
	{
        // 插补完成
        interp.is_interpolating = false;
		step_Error_Flag = false;  		  //标志位清除
		HAL_TIM_Base_Stop_IT(&htim3);  // 停止定时器
        return;
    }
	
     // 等待前一步完成再执行下一步
    if (Tripod_X.is_running || Tripod_Y.is_running) {
        return; // 如果电机正在运行，不进行新的操作
    }

    // Bresenham算法决策
    if (interp.abs_delta_x > interp.abs_delta_y) {
        // X轴为主轴
        interp.error += interp.abs_delta_y;
        if (interp.error >= interp.abs_delta_x) {
            // 需要在Y轴上移动
            interp.error -= interp.abs_delta_x;
            // 同时移动X和Y
            next_x = interp.x_dir == 0 ? interp.current_x + 1 : interp.current_x - 1;
            next_y = interp.y_dir == 1 ? interp.current_y + 1 : interp.current_y - 1;
            Tripod_PID_Goto(next_x, next_y, 3);
            interp.current_x = next_x;
            interp.current_y = next_y;
        } else {
            // 只在X轴上移动
			next_y = interp.current_y; // 确保next_x初始化为当前X值
            next_x = interp.x_dir == 0 ? interp.current_x + 1 : interp.current_x - 1;
            Tripod_PID_Goto(next_x, next_y, 3);
            interp.current_x = next_x;
        }
    } else {
        // Y轴为主轴
        interp.error += interp.abs_delta_x;
        if (interp.error >= interp.abs_delta_y) {
            // 需要在X轴上移动
            interp.error -= interp.abs_delta_y;
            // 同时移动X和Y
            next_x = interp.x_dir == 0 ? interp.current_x + 1 : interp.current_x - 1;
            next_y = interp.y_dir == 1 ? interp.current_y + 1 : interp.current_y - 1;
            Tripod_PID_Goto(next_x, next_y, 3);
            interp.current_x = next_x;
            interp.current_y = next_y;
        } else {
            // 只在Y轴上移动
			next_x = interp.current_x; // 确保next_x初始化为当前X值
            next_y = interp.y_dir == 1 ? interp.current_y + 1 : interp.current_y - 1;
            Tripod_PID_Goto(next_x, next_y, 3);
            interp.current_y = next_y;
        }
    }
    interp.current_step++;
}


// 主插补函数
void Tripod_Interpolation_Move(int16_t target_x, int16_t target_y)
{
    // 等待当前插补完成
    while(interp.is_interpolating)
	{
		HAL_Delay(1);  // 避免空转占用CPU
	}
	
	// 等待所有电机停止
     while(Tripod_X.is_running || Tripod_Y.is_running) {
        HAL_Delay(1);
    }
	 
    // 初始化插补参数
	interp.current_x = current_x;
	interp.current_y = current_y;
    interp.target_x = target_x;   //交接目标坐标
    interp.target_y = target_y;
    interp.delta_x = target_x - current_x;
    interp.delta_y = target_y - current_y;
    interp.abs_delta_x = abs(interp.delta_x);
    interp.abs_delta_y = abs(interp.delta_y);
	
    // 设置方向
    interp.x_dir = (interp.delta_x >= 0) ? 0 : 1;
    interp.y_dir = (interp.delta_y >= 0) ? 1 : 0;
    
   // 计算总步数
    interp.total_steps = (interp.abs_delta_x > interp.abs_delta_y) ? 
                          interp.abs_delta_x : interp.abs_delta_y;

    
    if (interp.total_steps == 0) {
        return; // 无需移动
    }
    
    // 初始化Bresenham算法参数
    interp.error = 0;
    interp.current_step = 0;
	
	// 启动插补
    interp.is_interpolating = true;
	
    // 启动插补
    __HAL_TIM_CLEAR_IT(&htim3, TIM_IT_UPDATE);
    HAL_TIM_Base_Start_IT(&htim3);	
}

// 初始化
void Interpolation_Init(void)
{
    interp.current_x = 0;
    interp.current_y = 0;
    interp.is_interpolating = false;
}

//PID
static void Tripod_PID_Stop(void)
{
    // 计算像素误差
    float x_error = current_x - current_x;
    float y_error = current_y - current_y;	
	
	if ((abs((int)x_error) <= 4) && (abs((int)y_error) <= 4))
	{
		return;
	}
// 计算PID输出
    float x_adjustment = PID_Calculate(&pid_x, current_x, current_x);
    float y_adjustment = PID_Calculate(&pid_y, current_y, current_y);
	
    // 将PID输出转换为角度调整
    // 这里需要根据实际系统特性调整转换关系
    float x_angle_change = x_adjustment * 0.07f; // 调整系数
    float y_angle_change = y_adjustment * 0.07f;
    
    // 应用角度调整
    Tripod_X_Angle(Tripod_X.angle + x_angle_change);
    Tripod_Y_Angle(Tripod_Y.angle + y_angle_change);
}


int16_t PID_text[4][2] = {0};
static uint8_t PID_text_index = 0;
static uint16_t stable_count = 0;
/*云台PID测试*/
void Tripod_PID_Text(void)
{
	if (Task_Stop_num % 2 == 1)		//即第一次或者第三次按下，即停止
	{
		if (Task_Stop_Plas)
		{
			PID_Init(&pid_x, 0, 0, 0, 20, 500);
			PID_Init(&pid_y, 0, 0, 0, 20, 500);
			Task_Stop_Plas = false;
		}
//		Tripod_PID_Stop();
		return;
	}
	else
	{
		if (Task_Stop_Plas)
		{
			PID_Init(&pid_x, 0.2, 0, 0, 20, 500);
			PID_Init(&pid_y, 0.2, 0, 0, 20, 500);
			Task_Stop_Plas = false;
		}
	}
	
    // 计算像素误差
    float x_error = PID_text[PID_text_index % 4][0] - current_x;
    float y_error = PID_text[PID_text_index % 4][1] - current_y;	
	
// 计算PID输出
    float x_adjustment = PID_Calculate(&pid_x, current_x, PID_text[PID_text_index % 4][0]);
    float y_adjustment = PID_Calculate(&pid_y, current_y, PID_text[PID_text_index % 4][1]);
	
    // 将PID输出转换为角度调整
    // 这里需要根据实际系统特性调整转换关系
    float x_angle_change = x_adjustment * 0.07f; // 调整系数
    float y_angle_change = y_adjustment * 0.07f;
    
    // 应用角度调整
    Tripod_X_Angle(Tripod_X.angle + x_angle_change);
    Tripod_Y_Angle(Tripod_Y.angle + y_angle_change);

	if ((abs((int)x_error) <= 4) && (abs((int)y_error) <= 4))
	{
       if (++stable_count > 20)
        {
            stable_count = 0;
            if (++PID_text_index == 5)
            {
                PID_text_index = 0;
				Task3_Start = false;
				HAL_UART_Transmit(&huart3, "STOP", 4, HAL_MAX_DELAY);   //串口3发送消息 - 给Openmv发送停止信号
            }
        }
	}
	else
	{
		stable_count = 0;
	}
}

/*任务一PID*/
void Tripod_PID_Task1(void)
{
    // 计算像素误差
    float x_error = Task1_step[4][0] - current_x;
    float y_error = Task1_step[4][1] - current_y;	
	
// 计算PID输出
    float x_adjustment = PID_Calculate(&pid_x, current_x, Task1_step[4][0]);
    float y_adjustment = PID_Calculate(&pid_y, current_y, Task1_step[4][1]);
	
    // 将PID输出转换为角度调整
    // 这里需要根据实际系统特性调整转换关系
    float x_angle_change = x_adjustment * 0.07f; // 调整系数
    float y_angle_change = y_adjustment * 0.07f;
    
    // 应用角度调整
    Tripod_X_Angle(Tripod_X.angle + x_angle_change);
    Tripod_Y_Angle(Tripod_Y.angle + y_angle_change);

	if ((abs((int)x_error) <= 4) && (abs((int)y_error) <= 4))
	{
       if (++stable_count > 20)
        {
            stable_count = 0;
        }
	}
	else
	{
		stable_count = 0;
	}
}


// 添加一个新函数，结合插补和PID
void Tripod_PID_Interpolation_Line(int16_t points[][2], uint8_t num_points)
{
    static uint8_t current_point = 0;
    
    // 如果插补正在进行，检查是否已完成当前段
    if(!interp.is_interpolating) {
        // 当前段插补完成，检查是否需要启动下一段
        if(current_point < num_points) {
            // 开始新的插补
            Tripod_Interpolation_Move(points[current_point][0], points[current_point][1]);
			current_point++;
        } else {
            current_point = 0;
			if (Task1_Start) 		//如果是任务一调度，则到达后直接停止
			{
//				Task1_Start = false;
			}
			else if (Task2_Start)	//如果是任务二调度，则到达第三个点后，再到第一个点，然后停止
			{
				Tripod_Interpolation_Move(points[0][0], points[0][1]);	
				Task2_Start = false;
			}

        }
    } else {
		HAL_Delay(1);
    }
}

