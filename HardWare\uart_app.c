#include "uart_app.h"

uint8_t uart1_rx_dma_buf[128];
uint8_t uart3_rx_dma_buf[128];
uint8_t uart1_rx_buf[128];
uint8_t uart3_rx_buf[128];
bool uart1_rx_flag = false;
bool uart3_rx_flag = false;

int16_t X_Step;
int16_t Y_Step;


uint8_t i = 0;
int16_t Task1_step[5][2] = {0};   //X轴任务一点位

bool Task1_Start = false;
bool Task2_Start = false;
bool Task3_Start = false;
bool Task_Stop_Plas = false;
uint8_t Task_Stop_num = 0;

void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
	if(huart -> Instance == USART1)
	{
		for (uint8_t i = 0; i < Size; i++)
		{
			uart1_rx_buf[i] = uart1_rx_dma_buf[i];
		}
		uart1_rx_flag = true;
//		if(uart1_rx_dma_buf[0] == 'r' || uart1_rx_dma_buf[0] == 'R')   //如果接收到R，就停止当前任务，前往中点
//		{
//			
//		}
		memset(uart1_rx_dma_buf, 0, sizeof(uart1_rx_dma_buf));  //清除缓存数据 --- 不能用 strlen
	}
	else if (huart -> Instance == USART3)
	{
		for (uint8_t i = 0; i < Size; i++)
		{
			uart3_rx_buf[i] = uart3_rx_dma_buf[i];
		}
		uart3_rx_flag = true;
		memset(uart3_rx_dma_buf, 0, sizeof(uart3_rx_dma_buf));  //清除缓存数据 --- 不能用 strlen
	}
}


void uart_task(void)
{
	if(uart1_rx_flag == false)
		return;

	
	if (uart1_rx_buf[0] == 'x' || uart1_rx_buf[0] == 'X')    //X轴转动
	{
		X_Step = (uart1_rx_buf[2] - 48) * 100 + (uart1_rx_buf[3] - 48) * 10 + (uart1_rx_buf[4] - 48);	//计算步进值
		
		if(uart1_rx_buf[1] == '+')    //左转
		{
			Step_Motor_X_Dir(0);
		}
		else if (uart1_rx_buf[1] == '-')  //右转
		{
			Step_Motor_X_Dir(1);
			X_Step = -X_Step;
		}
		printf("X_Step = %d, Y_Step = %d\r\n", X_Step, Y_Step);
		Tripod_X_Abs_Step(X_Step);
	}
	
	else if (uart1_rx_buf[0] == 'y' || uart1_rx_buf[0] == 'Y')    //Y轴转动
	{
		Y_Step = (uart1_rx_buf[2] - 48) * 100 + (uart1_rx_buf[3] - 48) * 10 + (uart1_rx_buf[4] - 48);
		
		if (uart1_rx_buf[1] == '+')	  //上转
		{
			Step_Motor_Y_Dir(1);  
		}
		else if (uart1_rx_buf[1] == '-')   //下转
		{
			Step_Motor_Y_Dir(0);
			Y_Step = -Y_Step;
		}
		printf("X_Step = %d, Y_Step = %d\r\n", X_Step, Y_Step);
		Tripod_Y_Abs_Step(Y_Step);
	}
	
	else if (uart1_rx_buf[0] == 'O')
	{
		
		if (uart1_rx_buf[1] == 'K')
		{
										//收到OK，记录坐标
//			Task1_step[i][0] = X_Step;    //记录点位
//			Task1_step[i][1] = Y_Step;
//			i++;   						  //切换下一个点位存储
			printf("OK:: X_Step = %d, Y_Step = %d\r\n", X_Step, Y_Step);
			HAL_UART_Transmit(&huart3, "JX", 2, HAL_MAX_DELAY);   //串口3发送消息
		}
	}
	else if (uart1_rx_buf[0] == 'A')
	{
		Task1_step[i][0] = current_x;
		Task1_step[i][1] = current_y;
		i++;
		printf("OK:: X_Step = %d, Y_Step = %d\r\n", current_x, current_y);
	}
	else if (uart1_rx_buf[0] == 'S' && uart1_rx_buf[1] == '1')   //开始任务一
	{
		HAL_UART_Transmit(&huart3, "REST", 4, HAL_MAX_DELAY);   //串口3发送消息 - 给Openmv发送复位信号
		printf("Starting Task 1 !!!\r\n");
		Task1_Start = true;
	}
	else if (uart1_rx_buf[0] == 'S' && uart1_rx_buf[1] == '2')   //开始任务二
	{
		printf("Starting Task 2 !!!\r\n");
		Task2_Start = true;	
	}
	else if (uart1_rx_buf[0] == 'S' && uart1_rx_buf[1] == '3')   //开始任务三
	{
		HAL_UART_Transmit(&huart3, "S3", 2, HAL_MAX_DELAY);   //串口3发送消息 - 给Openmv发送复位信号
		printf("Starting Task 3 !!!\r\n");
		Task3_Start = true;	
	}	
	else if (uart1_rx_buf[0] == 'T')   //停止
	{
		printf("Stoping\r\n");
		Task1_Start = false;
		Task2_Start = false;
		Task3_Start = false;
	}
	else if (strncmp((const char*)uart1_rx_buf, "FHT", 3) == 0)		//发挥题暂停
	{
		Task_Stop_Plas = true;
		Task_Stop_num += 1;
	}
	
	memset(uart1_rx_buf, 0, sizeof(uart1_rx_buf));  //清除缓存数据
	uart1_rx_flag = false;
}


void uart3_task(void)
{
	if(uart3_rx_flag == false)
		return;
	
	if (uart3_rx_buf[0] == 'G' && uart3_rx_buf[1] == 'D')  //读取坐标
	{
		if (uart3_rx_buf[2] == 'x')
		{
			current_x = (uart3_rx_buf[3] - 48) * 100 + (uart3_rx_buf[4] - 48) * 10 + (uart3_rx_buf[5] - 48);	//获取当前X位置
		}
		if (uart3_rx_buf[6] == 'y')
		{
			current_y = (uart3_rx_buf[7] - 48) * 100 + (uart3_rx_buf[8] - 48) * 10 + (uart3_rx_buf[9] - 48);	//获取当Y前位置
		}
	}
	else if (uart3_rx_buf[0] == 'J' && uart3_rx_buf[1] == 'X')  //读取矩形内框坐标
	{
		if (uart3_rx_buf[3] == 'x')  //左上点
		{
			PID_text[0][0] = (uart3_rx_buf[4] - 48) * 100 + (uart3_rx_buf[5] - 48) * 10 + (uart3_rx_buf[6] - 48);
		}
		if (uart3_rx_buf[7] == 'y')
		{
			PID_text[0][1] = (uart3_rx_buf[8] - 48) * 100 + (uart3_rx_buf[9] - 48) * 10 + (uart3_rx_buf[10] - 48);
		}
		
		if (uart3_rx_buf[12] == 'x') //右上点
		{
			PID_text[1][0] = (uart3_rx_buf[13] - 48) * 100 + (uart3_rx_buf[14] - 48) * 10 + (uart3_rx_buf[15] - 48);
		}
		if (uart3_rx_buf[16] == 'y')
		{
			PID_text[1][1] = (uart3_rx_buf[17] - 48) * 100 + (uart3_rx_buf[18] - 48) * 10 + (uart3_rx_buf[19] - 48);
		}
		
		if (uart3_rx_buf[21] == 'x')   //右下点
		{
			PID_text[2][0] = (uart3_rx_buf[22] - 48) * 100 + (uart3_rx_buf[23] - 48) * 10 + (uart3_rx_buf[24] - 48);
		}
		if (uart3_rx_buf[25] == 'y')
		{
			PID_text[2][1] = (uart3_rx_buf[26] - 48) * 100 + (uart3_rx_buf[27] - 48) * 10 + (uart3_rx_buf[28] - 48);
		}
		
		if (uart3_rx_buf[30] == 'x') //左下点
		{
			PID_text[3][0] = (uart3_rx_buf[31] - 48) * 100 + (uart3_rx_buf[32] - 48) * 10 + (uart3_rx_buf[33] - 48);
		}
		if (uart3_rx_buf[34] == 'y')
		{
			PID_text[3][1] = (uart3_rx_buf[35] - 48) * 100 + (uart3_rx_buf[36] - 48) * 10 + (uart3_rx_buf[37] - 48);
		}		
	}
	
	memset(uart3_rx_buf, 0, sizeof(uart3_rx_buf));  //清除缓存数据
	uart3_rx_flag = false;	
}

