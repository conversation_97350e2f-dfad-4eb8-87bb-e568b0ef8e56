#ifndef __BSP_SYSTEM_H
#define __BSP_SYSTEM_H

#include "main.h"
#include "usart.h"
#include "dma.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdarg.h>
#include <stdbool.h>

#include "task.h"

#include "PID.h"
#include "servo_app.h"
#include "laser_app.h"
#include "uart_app.h"



extern uint8_t uart1_rx_dma_buf[128];
extern uint8_t uart3_rx_dma_buf[128];

extern bool Task1_Start;   //任务一启停标志位
extern bool Task2_Start;   //任务二启停标志位
extern bool Task3_Start;   //任务三启停标志位
extern bool Task_Stop_Plas;
extern uint8_t Task_Stop_num;

extern int16_t Task1_step[][2];;   //X轴任务一点位

extern int16_t current_x, current_y;
extern int16_t PID_text[4][2];
void servo_system_init(void);

#endif
