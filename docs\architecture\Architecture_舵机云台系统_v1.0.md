# 系统架构设计文档 - 舵机云台系统改造

## 1. 文档信息

| 项目名称 | 23年电赛E题舵机云台系统架构设计 |
|---------|--------------------------------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-01-24 |
| 负责人 | Bob (架构师) |
| 审核人 | Mike (团队领袖) |

## 2. 现状分析

### 2.1 当前系统架构
test_uart工程目前采用**混合架构**，同时包含步进电机和舵机控制代码：

```
┌─────────────────────────────────────────────────────────────┐
│                    test_uart 当前架构                        │
├─────────────────────────────────────────────────────────────┤
│  应用层 (APP)                                               │
│  ├── uart_app.c     - 串口通信处理                         │
│  ├── servo.c        - 舵机状态变量                         │
│  └── pid001.c       - PID控制算法                          │
├─────────────────────────────────────────────────────────────┤
│  硬件抽象层 (HardWare)                                      │
│  ├── servo_app.c    - 【混合】步进电机 + 部分舵机控制      │
│  └── uart_app.c     - 【已有】舵机控制函数                 │
├─────────────────────────────────────────────────────────────┤
│  HAL驱动层 (Core)                                           │
│  ├── tim.c          - 定时器配置 (TIM1/TIM2/TIM3)          │
│  ├── gpio.c         - GPIO配置                             │
│  └── usart.c        - 串口配置                             │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 问题识别

**架构冲突**：
- `Tripod_X_Angle()` / `Tripod_Y_Angle()` 仍调用步进电机函数
- `s20_set_angle()` 舵机控制函数已存在但未被上层调用
- TIM1用于步进电机脉冲，TIM2/TIM3用于舵机PWM，资源冲突

**代码重复**：
- `angle_to_pulse()` 和 `s20_set_angle()` 中都有角度转换逻辑
- 步进电机的GPIO控制占用了PA0-PA5引脚

## 3. 目标架构设计

### 3.1 统一舵机控制架构

```
┌─────────────────────────────────────────────────────────────┐
│                  舵机云台系统目标架构                        │
├─────────────────────────────────────────────────────────────┤
│  应用层 (Application Layer)                                 │
│  ├── uart_app.c     - 串口通信处理 (保持不变)              │
│  ├── servo_tasks.c  - 任务调度和PID控制                    │
│  └── main.c         - 主循环和系统初始化                   │
├─────────────────────────────────────────────────────────────┤
│  控制层 (Control Layer)                                     │
│  ├── servo_control.c - 【新】统一舵机控制接口              │
│  ├── pid_control.c   - PID算法实现                         │
│  └── angle_mapping.c - 角度映射和转换                      │
├─────────────────────────────────────────────────────────────┤
│  硬件抽象层 (Hardware Abstraction Layer)                   │
│  ├── servo_hal.c    - 舵机硬件接口                         │
│  ├── pwm_driver.c   - PWM驱动程序                          │
│  └── gpio_config.c  - GPIO配置管理                         │
├─────────────────────────────────────────────────────────────┤
│  HAL驱动层 (HAL Driver Layer)                               │
│  ├── tim.c          - 定时器配置 (仅TIM2/TIM3 PWM)         │
│  ├── gpio.c         - GPIO配置                             │
│  └── usart.c        - 串口配置                             │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 核心接口设计

#### 3.2.1 统一舵机控制接口
```c
// 舵机轴向枚举
typedef enum {
    SERVO_X_AXIS = 0,    // X轴 (0-270度)
    SERVO_Y_AXIS = 1     // Y轴 (0-180度)
} servo_axis_t;

// 舵机状态结构体
typedef struct {
    float current_angle;     // 当前角度
    float target_angle;      // 目标角度
    float max_angle;         // 最大角度限制
    bool is_moving;          // 是否正在运动
    uint32_t last_update;    // 最后更新时间
} servo_state_t;

// 核心控制接口
bool servo_set_angle(servo_axis_t axis, float angle);
float servo_get_angle(servo_axis_t axis);
bool servo_is_moving(servo_axis_t axis);
void servo_system_init(void);
```

#### 3.2.2 角度转换接口
```c
// 角度到PWM脉宽转换
uint32_t angle_to_pulse(float angle, servo_axis_t axis);

// 脉宽到角度转换
float pulse_to_angle(uint32_t pulse, servo_axis_t axis);

// 角度范围验证
bool validate_angle_range(float angle, servo_axis_t axis);
```

## 4. 硬件配置方案

### 4.1 定时器资源分配

| 定时器 | 用途 | 配置参数 | PWM通道 | GPIO引脚 |
|--------|------|----------|---------|----------|
| TIM1 | **移除** | - | - | - |
| TIM2 | X轴舵机PWM | 预分频:72-1, 周期:20000-1 | CH1 | PA0 |
| TIM3 | Y轴舵机PWM | 预分频:72-1, 周期:20000-1 | CH1 | PA6 |

**PWM参数计算**：
- 系统时钟：72MHz
- 预分频后：1MHz (1us分辨率)
- PWM周期：20ms (20000计数)
- 脉宽范围：500-2500us (500-2500计数)

### 4.2 GPIO资源重新分配

| 引脚 | 原用途 | 新用途 | 配置 |
|------|--------|--------|------|
| PA0 | 步进电机X轴使能 | X轴舵机PWM (TIM2_CH1) | AF_PP |
| PA1 | 步进电机X轴方向 | **释放** | 可用于激光器控制 |
| PA2 | 步进电机X轴脉冲 | **释放** | 可用于其他功能 |
| PA3 | 步进电机Y轴使能 | **释放** | 可用于其他功能 |
| PA4 | 步进电机Y轴方向 | **释放** | 可用于其他功能 |
| PA5 | 步进电机Y轴脉冲 | **释放** | 可用于其他功能 |
| PA6 | - | Y轴舵机PWM (TIM3_CH1) | AF_PP |

## 5. 软件模块设计

### 5.1 舵机控制模块 (servo_control.c)

**职责**：提供统一的舵机控制接口
**核心函数**：
```c
// 初始化舵机系统
void servo_system_init(void) {
    // 初始化PWM定时器
    HAL_TIM_PWM_Start(&htim2, TIM_CHANNEL_1);  // X轴
    HAL_TIM_PWM_Start(&htim3, TIM_CHANNEL_1);  // Y轴
    
    // 设置初始角度
    servo_set_angle(SERVO_X_AXIS, 135.0f);    // X轴中位
    servo_set_angle(SERVO_Y_AXIS, 90.0f);     // Y轴中位
}

// 设置舵机角度
bool servo_set_angle(servo_axis_t axis, float angle) {
    if (!validate_angle_range(angle, axis)) {
        return false;
    }
    
    uint32_t pulse = angle_to_pulse(angle, axis);
    TIM_HandleTypeDef *htim = (axis == SERVO_X_AXIS) ? &htim2 : &htim3;
    
    __HAL_TIM_SET_COMPARE(htim, TIM_CHANNEL_1, pulse);
    servo_states[axis].current_angle = angle;
    servo_states[axis].last_update = HAL_GetTick();
    
    return true;
}
```

### 5.2 角度控制适配层

**重构策略**：保持现有接口，内部调用舵机控制
```c
// 重构后的Tripod_X_Angle函数
void Tripod_X_Angle(float X_Angle) {
    // 移除步进电机相关代码
    // while(Tripod_X.is_running);  // 删除
    // Step_Motor_X_Dir(0);         // 删除
    // Tripod_X_Step(step);         // 删除
    
    // 直接调用舵机控制接口
    if (servo_set_angle(SERVO_X_AXIS, X_Angle)) {
        Tripod_X.angle = X_Angle;   // 保持状态同步
    }
}

// 重构后的Tripod_Y_Angle函数
void Tripod_Y_Angle(float Y_Angle) {
    if (servo_set_angle(SERVO_Y_AXIS, Y_Angle)) {
        Tripod_Y.angle = Y_Angle;
    }
}
```

### 5.3 PID控制适配

**参数调整**：
```c
// 舵机专用PID参数
typedef struct {
    float kp;           // 比例系数
    float ki;           // 积分系数  
    float kd;           // 微分系数
    float max_output;   // 最大输出限制
    float deadband;     // 死区范围
} servo_pid_config_t;

// X轴和Y轴分别配置
servo_pid_config_t pid_config_x = {
    .kp = 0.15f,        // 降低比例系数，减少超调
    .ki = 0.01f,        // 增加积分项，消除稳态误差
    .kd = 0.05f,        // 增加微分项，提高响应速度
    .max_output = 5.0f, // 限制最大角度变化
    .deadband = 2.0f    // 死区范围，减少抖动
};
```

## 6. 数据流设计

### 6.1 控制数据流
```
串口指令 → uart_app.c → 指令解析 → servo_set_angle() → PWM输出 → 舵机动作
    ↓
OpenMV反馈 → uart3_task() → PID计算 → 角度调整 → servo_set_angle()
```

### 6.2 状态数据流
```
舵机当前角度 ← servo_get_angle() ← servo_states[] ← PWM更新回调
    ↓
PID控制器 → 误差计算 → 输出限制 → 角度调整指令
```

## 7. 接口兼容性设计

### 7.1 保持现有接口
为确保上层应用无需修改，保持以下接口不变：
- `Tripod_X_Angle(float X_Angle)`
- `Tripod_Y_Angle(float Y_Angle)`  
- `uart1_task()` / `uart3_task()`
- PID控制相关函数

### 7.2 内部实现重构
```c
// 原接口保持，内部实现改为调用舵机控制
void Tripod_X_Angle(float X_Angle) {
    servo_set_angle(SERVO_X_AXIS, X_Angle);
    Tripod_X.angle = X_Angle;  // 保持状态同步
}
```

## 8. 错误处理与安全机制

### 8.1 角度范围保护
```c
bool validate_angle_range(float angle, servo_axis_t axis) {
    float max_angle = (axis == SERVO_X_AXIS) ? 270.0f : 180.0f;
    return (angle >= 0.0f && angle <= max_angle);
}
```

### 8.2 PWM输出保护
```c
uint32_t angle_to_pulse(float angle, servo_axis_t axis) {
    float max_angle = (axis == SERVO_X_AXIS) ? 270.0f : 180.0f;
    
    // 角度限制
    if (angle < 0.0f) angle = 0.0f;
    if (angle > max_angle) angle = max_angle;
    
    // 脉宽计算和限制
    uint32_t pulse = 500 + (uint32_t)((angle / max_angle) * 2000);
    if (pulse < 500) pulse = 500;
    if (pulse > 2500) pulse = 2500;
    
    return pulse;
}
```

## 9. 性能优化策略

### 9.1 响应速度优化
- 移除步进电机的`while(is_running)`等待
- 舵机控制为即时响应，无需等待完成
- 优化PID控制周期，提高响应频率

### 9.2 精度优化
- 使用浮点角度控制，精度0.1度
- PWM分辨率1us，角度分辨率约0.18度
- 增加角度反馈机制，提高控制精度

## 10. 实施计划

### 10.1 分阶段实施
1. **阶段1**：定时器PWM配置优化
2. **阶段2**：舵机控制接口统一
3. **阶段3**：步进电机代码清理
4. **阶段4**：角度控制函数重构
5. **阶段5**：PID参数调优

### 10.2 风险控制
- 每个阶段完成后进行编译验证
- 保留原始代码备份
- 分步骤测试，确保功能正常

## 11. 验收标准

### 11.1 功能验收
- [ ] 舵机角度控制精度±1度
- [ ] 系统响应时间<200ms
- [ ] 串口通信功能正常
- [ ] PID控制稳定

### 11.2 代码质量验收
- [ ] 编译无错误无警告
- [ ] 步进电机代码完全移除
- [ ] 接口统一且易于使用
- [ ] 代码结构清晰，注释完整

---

**文档状态**：已完成  
**下一步行动**：开始定时器PWM配置优化